using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GMCadiomChat.Desktop.WinForms.Services;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Enums;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace GMCadiomChat.Desktop.WinForms.ViewModels;

public partial class ChatViewModel : ObservableObject
{
    private readonly SignalRChatClient _chatClient;
    private NotificationService _notificationService;

    [ObservableProperty]
    private string username = string.Empty;

    [ObservableProperty]
    private string email = string.Empty;

    [ObservableProperty]
    private bool isConnected;

    [ObservableProperty]
    private string connectionStatus = "Disconnected";

    [ObservableProperty]
    private ClientDto? currentUser;

    [ObservableProperty]
    private ClientViewModel? selectedUser;

    [ObservableProperty]
    private string messageText = string.Empty;

    [ObservableProperty]
    private bool isTyping;

    [ObservableProperty]
    private string typingIndicator = string.Empty;

    public ObservableCollection<ClientViewModel> OnlineUsers { get; } = new();
    public ObservableCollection<MessageViewModel> Messages { get; } = new();

    public ChatViewModel()
    {
        _chatClient = new SignalRChatClient();
        _notificationService = new NotificationService();
        
        SetupEventHandlers();
    }

    public ChatViewModel(Form mainForm) : this()
    {
        _notificationService = new NotificationService(mainForm);
    }

    private void SetupEventHandlers()
    {
        _chatClient.ConnectionStatusChanged += OnConnectionStatusChanged;
        _chatClient.MessageReceived += OnMessageReceived;
        _chatClient.UserConnected += OnUserConnected;
        _chatClient.UserDisconnected += OnUserDisconnected;
        _chatClient.UserStatusChanged += OnUserStatusChanged;
        _chatClient.BuzzReceived += OnBuzzReceived;
        _chatClient.TypingIndicatorReceived += OnTypingIndicatorReceived;
        _chatClient.MessageDelivered += OnMessageDelivered;
        _chatClient.MessageRead += OnMessageRead;
    }

    [RelayCommand]
    private async Task ConnectAsync()
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            MessageBox.Show("Please enter a username.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            await _chatClient.ConnectAsync();
            
            var loginRequest = new LoginRequest
            {
                Username = Username,
                Email = string.IsNullOrWhiteSpace(Email) ? $"{Username}@example.com" : Email
            };

            await _chatClient.JoinChatAsync(loginRequest);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to connect: {ex.Message}", "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    [RelayCommand]
    private async Task DisconnectAsync()
    {
        try
        {
            await _chatClient.LeaveChatAsync();
            await _chatClient.DisconnectAsync();
            
            OnlineUsers.Clear();
            Messages.Clear();
            CurrentUser = null;
            SelectedUser = null;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to disconnect: {ex.Message}", "Disconnect Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    [RelayCommand]
    private async Task SendMessageAsync()
    {
        if (string.IsNullOrWhiteSpace(MessageText) || !IsConnected)
            return;

        try
        {
            var request = new SendMessageRequest
            {
                ReceiverId = SelectedUser?.Id,
                MessageType = MessageType.Text,
                Content = MessageText.Trim()
            };

            await _chatClient.SendMessageAsync(request);
            MessageText = string.Empty;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to send message: {ex.Message}", "Send Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    [RelayCommand]
    private async Task SendBuzzAsync()
    {
        if (SelectedUser == null || !IsConnected)
            return;

        try
        {
            await _chatClient.SendBuzzAsync(SelectedUser.Id);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to send buzz: {ex.Message}", "Buzz Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public void SelectUser(ClientViewModel user)
    {
        SelectedUser = user;
        user.IsSelected = true;
        
        // Deselect other users
        foreach (var otherUser in OnlineUsers.Where(u => u != user))
        {
            otherUser.IsSelected = false;
        }

        // Load conversation history
        _ = LoadConversationHistoryAsync(user.Id);
    }

    private async Task LoadConversationHistoryAsync(Guid userId)
    {
        if (CurrentUser == null) return;

        try
        {
            Messages.Clear();
            await _chatClient.GetMessageHistoryAsync(userId, 0, 50);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to load conversation history: {ex.Message}", "Load Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public void HandleMessageTextChanged(string value)
    {
        MessageText = value;

        if (SelectedUser != null && IsConnected)
        {
            var newIsTyping = !string.IsNullOrWhiteSpace(value);
            if (newIsTyping != IsTyping)
            {
                IsTyping = newIsTyping;
                _ = _chatClient.SetTypingAsync(SelectedUser.Id, IsTyping);
            }
        }
    }

    private void OnConnectionStatusChanged(object? sender, string status)
    {
        ConnectionStatus = status;
        IsConnected = status == "Connected";
    }

    private void OnMessageReceived(object? sender, MessageDto message)
    {
        if (CurrentUser == null) return;

        var messageViewModel = new MessageViewModel(message, CurrentUser.Id);
        Messages.Add(messageViewModel);

        // Show notification if message is not from current user
        if (message.SenderId != CurrentUser.Id)
        {
            _ = _notificationService.ShowNotificationAsync(
                $"New message from {message.SenderUsername}",
                message.Content);
            
            _ = _notificationService.PlaySoundAsync("message");

            // Update unread count for sender
            var messageSender = OnlineUsers.FirstOrDefault(u => u.Id == message.SenderId);
            if (messageSender != null)
            {
                messageSender.UnreadCount++;
            }
        }
    }

    private void OnUserConnected(object? sender, ClientDto client)
    {
        // Don't add current user to the list
        if (CurrentUser != null && client.Id == CurrentUser.Id)
        {
            CurrentUser = client;
            return;
        }

        var existingUser = OnlineUsers.FirstOrDefault(u => u.Id == client.Id);
        if (existingUser != null)
        {
            existingUser.UpdateFrom(client);
        }
        else
        {
            OnlineUsers.Add(new ClientViewModel(client));
        }
    }

    private void OnUserDisconnected(object? sender, ClientDto client)
    {
        var user = OnlineUsers.FirstOrDefault(u => u.Id == client.Id);
        if (user != null)
        {
            OnlineUsers.Remove(user);
        }
    }

    private void OnUserStatusChanged(object? sender, ClientDto client)
    {
        var user = OnlineUsers.FirstOrDefault(u => u.Id == client.Id);
        if (user != null)
        {
            user.UpdateFrom(client);
        }
    }

    private void OnBuzzReceived(object? sender, (Guid UserId, string Username) buzz)
    {
        _ = _notificationService.ShowBuzzNotificationAsync(buzz.Username);
    }

    private void OnTypingIndicatorReceived(object? sender, (Guid UserId, string Username, bool IsTyping) typing)
    {
        if (typing.IsTyping)
        {
            TypingIndicator = $"{typing.Username} is typing...";
        }
        else
        {
            TypingIndicator = string.Empty;
        }
    }

    private void OnMessageDelivered(object? sender, Guid messageId)
    {
        var message = Messages.FirstOrDefault(m => m.Id == messageId);
        if (message != null)
        {
            message.IsDelivered = true;
        }
    }

    private void OnMessageRead(object? sender, Guid messageId)
    {
        var message = Messages.FirstOrDefault(m => m.Id == messageId);
        if (message != null)
        {
            message.IsRead = true;
        }
    }
}
